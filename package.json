{"name": "plume<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}